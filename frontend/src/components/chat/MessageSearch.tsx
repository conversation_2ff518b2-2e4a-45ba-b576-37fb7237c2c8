'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Search, X, ChevronUp, ChevronDown } from 'lucide-react';

interface SearchResult {
  messageId: string;
  content: string;
  sender: {
    firstName: string;
    lastName: string;
  };
  timestamp: string;
  context: string; // Surrounding text for context
}

interface MessageSearchProps {
  isOpen: boolean;
  onClose: () => void;
  onResultSelect: (messageId: string) => void;
  chatId?: string;
}

export default function MessageSearch({
  isOpen,
  onClose,
  onResultSelect,
  chatId
}: MessageSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Focus search input when opened
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Perform search
  useEffect(() => {
    const performSearch = async () => {
      if (!searchTerm.trim() || !chatId) {
        setResults([]);
        return;
      }

      setLoading(true);
      try {
        // TODO: Implement actual search API call
        // const response = await chatApi.searchMessages(chatId, searchTerm);
        // setResults(response.results);
        
        // Mock search results for now
        const mockResults: SearchResult[] = [
          {
            messageId: '1',
            content: `Found "${searchTerm}" in this message`,
            sender: { firstName: 'John', lastName: 'Doe' },
            timestamp: new Date().toISOString(),
            context: `...some context before ${searchTerm} and after...`
          }
        ];
        setResults(mockResults);
        setCurrentIndex(0);
      } catch (error) {
        console.error('Search failed:', error);
        setResults([]);
      } finally {
        setLoading(false);
      }
    };

    const debounceTimer = setTimeout(performSearch, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchTerm, chatId]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setCurrentIndex(prev => Math.min(prev + 1, results.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setCurrentIndex(prev => Math.max(prev - 1, 0));
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (results[currentIndex]) {
        onResultSelect(results[currentIndex].messageId);
        onClose();
      }
    }
  };

  const navigateResults = (direction: 'up' | 'down') => {
    if (direction === 'up') {
      setCurrentIndex(prev => Math.max(prev - 1, 0));
    } else {
      setCurrentIndex(prev => Math.min(prev + 1, results.length - 1));
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  if (!isOpen) return null;

  return (
    <Card className="absolute top-0 left-0 right-0 z-50 border-b shadow-lg">
      <CardContent className="p-4">
        <div className="flex items-center space-x-2 mb-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              ref={searchInputRef}
              placeholder="Search messages..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={handleKeyDown}
              className="pl-10 pr-20"
            />
            {results.length > 0 && (
              <div className="absolute right-12 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                <span className="text-xs text-gray-500">
                  {currentIndex + 1} of {results.length}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateResults('up')}
                  disabled={currentIndex === 0}
                  className="h-6 w-6 p-0"
                >
                  <ChevronUp className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigateResults('down')}
                  disabled={currentIndex === results.length - 1}
                  className="h-6 w-6 p-0"
                >
                  <ChevronDown className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {loading && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500 mx-auto"></div>
          </div>
        )}

        {!loading && searchTerm && results.length === 0 && (
          <div className="text-center py-4 text-gray-500">
            No messages found for &quot;{searchTerm}&quot;
          </div>
        )}

        {results.length > 0 && (
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {results.map((result, index) => (
              <div
                key={result.messageId}
                onClick={() => {
                  onResultSelect(result.messageId);
                  onClose();
                }}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  index === currentIndex
                    ? 'bg-green-50 border border-green-200'
                    : 'bg-gray-50 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm font-medium">
                    {result.sender.firstName} {result.sender.lastName}
                  </span>
                  <span className="text-xs text-gray-500">
                    {formatTimestamp(result.timestamp)}
                  </span>
                </div>
                <p className="text-sm text-gray-700 line-clamp-2">
                  {result.context}
                </p>
              </div>
            ))}
          </div>
        )}

        <div className="mt-4 text-xs text-gray-500">
          Use ↑↓ to navigate, Enter to select, Esc to close
        </div>
      </CardContent>
    </Card>
  );
}
