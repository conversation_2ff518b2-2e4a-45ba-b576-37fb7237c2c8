'use client';

import React, { useState, useEffect } from 'react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import DashboardLayout from '@/components/layout/DashboardLayout';
import {
  Coins,
  CreditCard,
  Smartphone,
  CheckCircle,
  History,
  Gift
} from 'lucide-react';

interface TokenPackage {
  id: string;
  tokens: number;
  priceKES: number;
  priceUSD: number;
  bonus: number;
  popular?: boolean;
  description: string;
}

interface Transaction {
  _id: string;
  type: 'purchase' | 'earned' | 'spent';
  amount: number;
  description: string;
  createdAt: string;
  status: 'completed' | 'pending' | 'failed';
}

const tokenPackages: TokenPackage[] = [
  {
    id: 'starter',
    tokens: 100,
    priceKES: 500,
    priceUSD: 5,
    bonus: 0,
    description: 'Perfect for getting started'
  },
  {
    id: 'popular',
    tokens: 250,
    priceKES: 1200,
    priceUSD: 12,
    bonus: 25,
    popular: true,
    description: 'Most popular choice'
  },
  {
    id: 'value',
    tokens: 500,
    priceKES: 2300,
    priceUSD: 23,
    bonus: 75,
    description: 'Best value for money'
  },
  {
    id: 'premium',
    tokens: 1000,
    priceKES: 4500,
    priceUSD: 45,
    bonus: 200,
    description: 'For power users'
  }
];

export default function TokensPage() {
  const [user, setUser] = useState<any>(null);
  const [selectedPackage, setSelectedPackage] = useState<TokenPackage | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'mpesa' | 'card'>('mpesa');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [customAmount, setCustomAmount] = useState('');


  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }

    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/tokens/history', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setTransactions(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
  };

  const handlePurchase = async () => {
    if (!selectedPackage) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/token-purchases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          packageId: selectedPackage.id,
          paymentMethod,
          phoneNumber: paymentMethod === 'mpesa' ? phoneNumber : undefined,
        }),
      });

      if (response.ok) {
        await response.json();
        // Handle successful purchase
        alert('Purchase initiated! You will receive an M-Pesa prompt shortly.');
        setSelectedPackage(null);
        setPhoneNumber('');
        fetchTransactions();
      } else {
        const error = await response.json();
        alert(error.message || 'Purchase failed');
      }
    } catch (error) {
      console.error('Error purchasing tokens:', error);
      alert('Purchase failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const calculateCustomTokens = (amount: string) => {
    const kes = parseFloat(amount);
    if (isNaN(kes) || kes <= 0) return 0;
    return Math.floor(kes / 5); // 5 KES per token
  };

  if (!user) {
    return null;
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Pedi Tokens</h1>
            <p className="text-gray-600">Purchase tokens to exchange for clothing items</p>
          </div>
          <div className="text-right">
            <div className="flex items-center gap-2">
              <Coins className="h-5 w-5 text-yellow-600" />
              <span className="text-2xl font-bold text-yellow-600">{user.pediTokens}</span>
            </div>
            <p className="text-sm text-gray-500">Current Balance</p>
          </div>
        </div>

        <Tabs defaultValue="purchase" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="purchase">Purchase Tokens</TabsTrigger>
            <TabsTrigger value="history">Transaction History</TabsTrigger>
          </TabsList>

          <TabsContent value="purchase" className="space-y-6">
            {/* Token Packages */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {tokenPackages.map((pkg) => (
                <Card 
                  key={pkg.id}
                  className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                    selectedPackage?.id === pkg.id 
                      ? 'ring-2 ring-[#01796F] border-[#01796F]' 
                      : 'hover:border-[#01796F]'
                  } ${pkg.popular ? 'relative' : ''}`}
                  onClick={() => setSelectedPackage(pkg)}
                >
                  {pkg.popular && (
                    <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-[#01796F]">
                      Most Popular
                    </Badge>
                  )}
                  
                  <CardHeader className="text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Coins className="h-8 w-8 text-yellow-600" />
                    </div>
                    <CardTitle className="text-2xl">{pkg.tokens} Tokens</CardTitle>
                    {pkg.bonus > 0 && (
                      <Badge variant="secondary" className="mx-auto">
                        +{pkg.bonus} Bonus
                      </Badge>
                    )}
                  </CardHeader>
                  
                  <CardContent className="text-center space-y-3">
                    <div>
                      <div className="text-2xl font-bold text-[#032221]">
                        KES {pkg.priceKES.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        ${pkg.priceUSD}
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600">{pkg.description}</p>
                    
                    <div className="text-xs text-gray-500">
                      {pkg.bonus > 0 ? (
                        <span>Total: {pkg.tokens + pkg.bonus} tokens</span>
                      ) : (
                        <span>{(pkg.priceKES / pkg.tokens).toFixed(1)} KES per token</span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Custom Amount */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gift className="h-5 w-5" />
                  Custom Amount
                </CardTitle>
                <CardDescription>
                  Purchase any amount of tokens (5 KES = 1 token)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <div className="flex-1">
                    <Label htmlFor="customAmount">Amount (KES)</Label>
                    <Input
                      id="customAmount"
                      type="number"
                      min="25"
                      value={customAmount}
                      onChange={(e) => setCustomAmount(e.target.value)}
                      placeholder="e.g., 1000"
                    />
                  </div>
                  <div className="flex-1">
                    <Label>Tokens You&apos;ll Get</Label>
                    <div className="h-10 flex items-center px-3 bg-gray-50 rounded-md">
                      <Coins className="h-4 w-4 text-yellow-600 mr-2" />
                      <span className="font-medium">
                        {calculateCustomTokens(customAmount)} tokens
                      </span>
                    </div>
                  </div>
                </div>
                
                <Button
                  onClick={() => {
                    const tokens = calculateCustomTokens(customAmount);
                    if (tokens > 0) {
                      setSelectedPackage({
                        id: 'custom',
                        tokens,
                        priceKES: parseFloat(customAmount),
                        priceUSD: parseFloat(customAmount) / 100,
                        bonus: 0,
                        description: 'Custom amount'
                      });
                    }
                  }}
                  disabled={calculateCustomTokens(customAmount) === 0}
                  variant="outline"
                  className="w-full"
                >
                  Select Custom Amount
                </Button>
              </CardContent>
            </Card>

            {/* Payment Section */}
            {selectedPackage && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Complete Purchase
                  </CardTitle>
                  <CardDescription>
                    You&apos;re purchasing {selectedPackage.tokens} tokens
                    {selectedPackage.bonus > 0 && ` + ${selectedPackage.bonus} bonus`}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Payment Method Selection */}
                  <div className="space-y-4">
                    <Label className="text-base font-medium">Payment Method</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <button
                        type="button"
                        onClick={() => setPaymentMethod('mpesa')}
                        className={`p-4 border rounded-lg text-center transition-colors ${
                          paymentMethod === 'mpesa'
                            ? 'border-[#01796F] bg-green-50 text-[#01796F]'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <Smartphone className="h-6 w-6 mx-auto mb-2" />
                        <p className="text-sm font-medium">M-Pesa</p>
                        <p className="text-xs text-gray-600">Mobile Money</p>
                      </button>

                      <button
                        type="button"
                        onClick={() => setPaymentMethod('card')}
                        className={`p-4 border rounded-lg text-center transition-colors ${
                          paymentMethod === 'card'
                            ? 'border-[#01796F] bg-green-50 text-[#01796F]'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <CreditCard className="h-6 w-6 mx-auto mb-2" />
                        <p className="text-sm font-medium">Card</p>
                        <p className="text-xs text-gray-600">Credit/Debit</p>
                      </button>
                    </div>
                  </div>

                  {/* M-Pesa Phone Number */}
                  {paymentMethod === 'mpesa' && (
                    <div className="space-y-2">
                      <Label htmlFor="phoneNumber">M-Pesa Phone Number</Label>
                      <Input
                        id="phoneNumber"
                        type="tel"
                        value={phoneNumber}
                        onChange={(e) => setPhoneNumber(e.target.value)}
                        placeholder="e.g., 254712345678"
                      />
                      <p className="text-xs text-gray-500">
                        Enter your M-Pesa registered phone number
                      </p>
                    </div>
                  )}

                  {/* Purchase Summary */}
                  <div className="p-4 bg-gray-50 rounded-lg space-y-2">
                    <div className="flex justify-between">
                      <span>Tokens:</span>
                      <span>{selectedPackage.tokens}</span>
                    </div>
                    {selectedPackage.bonus > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Bonus:</span>
                        <span>+{selectedPackage.bonus}</span>
                      </div>
                    )}
                    <hr />
                    <div className="flex justify-between font-medium">
                      <span>Total Tokens:</span>
                      <span>{selectedPackage.tokens + selectedPackage.bonus}</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg">
                      <span>Amount:</span>
                      <span>KES {selectedPackage.priceKES.toLocaleString()}</span>
                    </div>
                  </div>

                  {/* Purchase Button */}
                  <div className="flex gap-3">
                    <Button
                      variant="outline"
                      onClick={() => setSelectedPackage(null)}
                      className="flex-1"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handlePurchase}
                      disabled={loading || (paymentMethod === 'mpesa' && !phoneNumber)}
                      className="flex-1 bg-[#01796F] hover:bg-[#032221]"
                    >
                      {loading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Processing...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Pay KES {selectedPackage.priceKES.toLocaleString()}
                        </>
                      )}
                    </Button>
                  </div>

                  {paymentMethod === 'mpesa' && (
                    <Alert>
                      <Smartphone className="h-4 w-4" />
                      <AlertDescription>
                        You will receive an M-Pesa prompt on your phone to complete the payment.
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5" />
                  Transaction History
                </CardTitle>
              </CardHeader>
              <CardContent>
                {transactions.length === 0 ? (
                  <div className="text-center py-8">
                    <Coins className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No transactions yet</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {transactions.map((transaction) => (
                      <div key={transaction._id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`p-2 rounded-full ${
                            transaction.type === 'purchase' ? 'bg-blue-100' :
                            transaction.type === 'earned' ? 'bg-green-100' : 'bg-red-100'
                          }`}>
                            {transaction.type === 'purchase' ? (
                              <CreditCard className="h-4 w-4 text-blue-600" />
                            ) : transaction.type === 'earned' ? (
                              <Gift className="h-4 w-4 text-green-600" />
                            ) : (
                              <Coins className="h-4 w-4 text-red-600" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{transaction.description}</p>
                            <p className="text-sm text-gray-500">
                              {new Date(transaction.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className={`font-medium ${
                            transaction.type === 'spent' ? 'text-red-600' : 'text-green-600'
                          }`}>
                            {transaction.type === 'spent' ? '-' : '+'}{transaction.amount} tokens
                          </p>
                          <Badge variant={
                            transaction.status === 'completed' ? 'default' :
                            transaction.status === 'pending' ? 'secondary' : 'destructive'
                          }>
                            {transaction.status}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
}
